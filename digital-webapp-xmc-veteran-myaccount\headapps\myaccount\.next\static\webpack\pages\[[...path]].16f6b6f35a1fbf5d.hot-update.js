"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[[...path]]",{

/***/ "./src/components/common/CaseManagementWidget/CaseManagementWidget.tsx":
/*!*****************************************************************************!*\
  !*** ./src/components/common/CaseManagementWidget/CaseManagementWidget.tsx ***!
  \*****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CaseManagementWidget: function() { return /* binding */ CaseManagementWidget; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mantine/core */ \"./node_modules/@mantine/core/esm/index.js\");\n/* harmony import */ var _sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @sitecore-jss/sitecore-jss-nextjs */ \"./node_modules/@sitecore-jss/sitecore-jss-nextjs/dist/esm/index.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/script */ \"./node_modules/next/script.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_script__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var src_hoc_ApplicationInsightsLogger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/hoc/ApplicationInsightsLogger */ \"./src/hoc/ApplicationInsightsLogger.tsx\");\n/* harmony import */ var src_stores_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/stores/store */ \"./src/stores/store.ts\");\n/* harmony import */ var _PageBuilder_PageBuilder__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../PageBuilder/PageBuilder */ \"./src/components/common/PageBuilder/PageBuilder.tsx\");\n\r\nvar _s = $RefreshSig$();\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nconst CaseManagementWidget = (props)=>{\r\n    var _props_fields_LightningScriptUrl, _props_fields;\r\n    _s();\r\n    const context = (0,_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_6__.useSitecoreContext)();\r\n    const isPageEditing = context.sitecoreContext.pageEditing;\r\n    if (isPageEditing) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PageBuilder_PageBuilder__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\r\n        componentName: \"CaseManagementWidget\"\r\n    }, void 0, false, {\r\n        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n        lineNumber: 20,\r\n        columnNumber: 29\r\n    }, undefined);\r\n    let partnerNumber = undefined;\r\n    if (!isPageEditing) {\r\n        partnerNumber = (0,src_stores_store__WEBPACK_IMPORTED_MODULE_4__.useAppSelector)((state)=>{\r\n            var _state_authuser;\r\n            return state === null || state === void 0 ? void 0 : (_state_authuser = state.authuser) === null || _state_authuser === void 0 ? void 0 : _state_authuser.bpNumber;\r\n        });\r\n    }\r\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\r\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\r\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\r\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\r\n    const retryCountRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(0);\r\n    const maxRetries = 3;\r\n    const timeoutDuration = 30000; // 30 seconds\r\n    // Clear timeout on component unmount\r\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\r\n        return ()=>{\r\n            if (timeoutRef.current) {\r\n                clearTimeout(timeoutRef.current);\r\n            }\r\n        };\r\n    }, []);\r\n    function callLightning() {\r\n        try {\r\n            var _props_fields_SalesforceScriptUrl, _props_fields, _props_fields_LightningScriptUrl, _props_fields1, _props_fields_SalesforceScriptUrl1, _props_fields2;\r\n            console.log(\"Initializing Lightning component...\", {\r\n                partnerNumber,\r\n                salesforceScriptUrl: props === null || props === void 0 ? void 0 : (_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_SalesforceScriptUrl = _props_fields.SalesforceScriptUrl) === null || _props_fields_SalesforceScriptUrl === void 0 ? void 0 : _props_fields_SalesforceScriptUrl.value,\r\n                lightningScriptUrl: props === null || props === void 0 ? void 0 : (_props_fields1 = props.fields) === null || _props_fields1 === void 0 ? void 0 : (_props_fields_LightningScriptUrl = _props_fields1.LightningScriptUrl) === null || _props_fields_LightningScriptUrl === void 0 ? void 0 : _props_fields_LightningScriptUrl.value\r\n            });\r\n            // Set a timeout for the Lightning component loading\r\n            timeoutRef.current = setTimeout(()=>{\r\n                if (isLoading) {\r\n                    console.error(\"Lightning component loading timed out after\", timeoutDuration, \"ms\");\r\n                    setHasError(true);\r\n                    setErrorMessage(\"The customer request center is taking longer than expected to load. Please try refreshing the page.\");\r\n                    setIsLoading(false);\r\n                }\r\n            }, timeoutDuration);\r\n            // Check if Lightning is available\r\n            if (!window.$Lightning) {\r\n                throw new Error(\"Salesforce Lightning framework is not available\");\r\n            }\r\n            console.log(\"Lightning framework available, creating component...\");\r\n            window.$Lightning.use(\"c:CXT_SelftServeLtngOut\", ()=>{\r\n                console.log(\"Lightning app loaded, creating component...\");\r\n                window.$Lightning.createComponent(\"c:cXT_CaseMgmtSelfServe\", {\r\n                    bpNumber: partnerNumber ? partnerNumber : \"\"\r\n                }, \"lightningContainer\", (component)=>{\r\n                    // Clear timeout on successful load\r\n                    if (timeoutRef.current) {\r\n                        clearTimeout(timeoutRef.current);\r\n                    }\r\n                    setIsLoading(false);\r\n                    setHasError(false);\r\n                    console.log(\"Component loaded successfully: \", component);\r\n                });\r\n            }, props === null || props === void 0 ? void 0 : (_props_fields2 = props.fields) === null || _props_fields2 === void 0 ? void 0 : (_props_fields_SalesforceScriptUrl1 = _props_fields2.SalesforceScriptUrl) === null || _props_fields_SalesforceScriptUrl1 === void 0 ? void 0 : _props_fields_SalesforceScriptUrl1.value);\r\n        } catch (error) {\r\n            console.error(\"Error initializing Lightning component:\", error);\r\n            handleError(error);\r\n        }\r\n    }\r\n    function handleError(error) {\r\n        console.error(\"Lightning component error:\", error.message);\r\n        if (timeoutRef.current) {\r\n            clearTimeout(timeoutRef.current);\r\n        }\r\n        retryCountRef.current += 1;\r\n        if (retryCountRef.current < maxRetries) {\r\n            console.log(\"Retrying Lightning component initialization (attempt \".concat(retryCountRef.current + 1, \"/\").concat(maxRetries, \") after error: \").concat(error.message));\r\n            setTimeout(()=>{\r\n                callLightning();\r\n            }, 2000); // Wait 2 seconds before retry\r\n        } else {\r\n            setHasError(true);\r\n            setErrorMessage(\"Failed to load the customer request center after \".concat(maxRetries, \" attempts. Error: \").concat(error.message, \". Please refresh the page or contact support.\"));\r\n            setIsLoading(false);\r\n        }\r\n    }\r\n    function handleScriptError() {\r\n        console.error(\"Failed to load Salesforce Lightning script\");\r\n        setHasError(true);\r\n        setErrorMessage(\"Failed to load required scripts. Please check your internet connection and try again.\");\r\n        setIsLoading(false);\r\n    }\r\n    function retryLoading() {\r\n        setIsLoading(true);\r\n        setHasError(false);\r\n        setErrorMessage(\"\");\r\n        retryCountRef.current = 0;\r\n        callLightning();\r\n    }\r\n    // Show error state with retry option\r\n    if (hasError) {\r\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n            className: \"mb-20 sm:mb-0\",\r\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                className: \"w-full sm:w-[905px] case-manage\",\r\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                    className: \"flex flex-col items-center justify-center p-8 bg-red-50 border border-red-200 rounded-lg\",\r\n                    children: [\r\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                            className: \"text-red-600 text-lg font-semibold mb-4\",\r\n                            children: \"Unable to Load Customer Request Center\"\r\n                        }, void 0, false, {\r\n                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                            lineNumber: 138,\r\n                            columnNumber: 13\r\n                        }, undefined),\r\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                            className: \"text-red-700 text-center mb-6 max-w-md\",\r\n                            children: errorMessage\r\n                        }, void 0, false, {\r\n                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                            lineNumber: 141,\r\n                            columnNumber: 13\r\n                        }, undefined),\r\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\r\n                            onClick: retryLoading,\r\n                            className: \"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\r\n                            children: \"Try Again\"\r\n                        }, void 0, false, {\r\n                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                            lineNumber: 144,\r\n                            columnNumber: 13\r\n                        }, undefined),\r\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                            className: \"mt-4 text-sm text-gray-600 text-center\",\r\n                            children: [\r\n                                \"If the problem persists, please contact customer support at\",\r\n                                \" \",\r\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\r\n                                    href: \"tel:**************\",\r\n                                    className: \"text-blue-600 hover:underline\",\r\n                                    children: \"(*************\"\r\n                                }, void 0, false, {\r\n                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                                    lineNumber: 152,\r\n                                    columnNumber: 15\r\n                                }, undefined)\r\n                            ]\r\n                        }, void 0, true, {\r\n                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                            lineNumber: 150,\r\n                            columnNumber: 13\r\n                        }, undefined)\r\n                    ]\r\n                }, void 0, true, {\r\n                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                    lineNumber: 137,\r\n                    columnNumber: 11\r\n                }, undefined)\r\n            }, void 0, false, {\r\n                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                lineNumber: 136,\r\n                columnNumber: 9\r\n            }, undefined)\r\n        }, void 0, false, {\r\n            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n            lineNumber: 135,\r\n            columnNumber: 7\r\n        }, undefined);\r\n    }\r\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n        className: \"mb-20 sm:mb-0\",\r\n        children: [\r\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                className: \"w-full flex flex-col items-center justify-center p-8\",\r\n                children: [\r\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Loader, {}, void 0, false, {\r\n                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                        lineNumber: 166,\r\n                        columnNumber: 11\r\n                    }, undefined),\r\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                        className: \"mt-4 text-gray-600 text-center\",\r\n                        children: \"Loading Customer Request Center...\"\r\n                    }, void 0, false, {\r\n                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                        lineNumber: 167,\r\n                        columnNumber: 11\r\n                    }, undefined),\r\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                        className: \"mt-2 text-sm text-gray-500 text-center\",\r\n                        children: \"This may take a few moments\"\r\n                    }, void 0, false, {\r\n                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                        lineNumber: 170,\r\n                        columnNumber: 11\r\n                    }, undefined)\r\n                ]\r\n            }, void 0, true, {\r\n                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                lineNumber: 165,\r\n                columnNumber: 9\r\n            }, undefined),\r\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_script__WEBPACK_IMPORTED_MODULE_1___default()), {\r\n                src: props === null || props === void 0 ? void 0 : (_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_LightningScriptUrl = _props_fields.LightningScriptUrl) === null || _props_fields_LightningScriptUrl === void 0 ? void 0 : _props_fields_LightningScriptUrl.value,\r\n                strategy: \"lazyOnload\",\r\n                onLoad: callLightning,\r\n                onError: handleScriptError\r\n            }, void 0, false, {\r\n                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                lineNumber: 175,\r\n                columnNumber: 7\r\n            }, undefined),\r\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                className: \"w-full sm:w-[905px] case-manage\",\r\n                id: \"lightningContainer\"\r\n            }, void 0, false, {\r\n                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                lineNumber: 181,\r\n                columnNumber: 7\r\n            }, undefined)\r\n        ]\r\n    }, void 0, true, {\r\n        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n        lineNumber: 163,\r\n        columnNumber: 5\r\n    }, undefined);\r\n};\r\n_s(CaseManagementWidget, \"uCBgy6ZhWzIZbuVPSGIKFgISpl4=\", false, function() {\r\n    return [\r\n        _sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_6__.useSitecoreContext\r\n    ];\r\n});\r\n_c = CaseManagementWidget;\r\n\r\nconst Component = (0,_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_6__.withDatasourceCheck)()(CaseManagementWidget);\r\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c1 = (0,src_hoc_ApplicationInsightsLogger__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(Component, Component.name));\r\nvar _c, _c1;\r\n$RefreshReg$(_c, \"CaseManagementWidget\");\r\n$RefreshReg$(_c1, \"%default%\");\r\n\r\n\r\n;\r\n    // Wrapped in an IIFE to avoid polluting the global scope\r\n    ;\r\n    (function () {\r\n        var _a, _b;\r\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\r\n        // to extract CSS. For backwards compatibility, we need to check we're in a\r\n        // browser context before continuing.\r\n        if (typeof self !== 'undefined' &&\r\n            // AMP / No-JS mode does not inject these helpers:\r\n            '$RefreshHelpers$' in self) {\r\n            // @ts-ignore __webpack_module__ is global\r\n            var currentExports = module.exports;\r\n            // @ts-ignore __webpack_module__ is global\r\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\r\n            // This cannot happen in MainTemplate because the exports mismatch between\r\n            // templating and execution.\r\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\r\n            // A module can be accepted automatically based on its exports, e.g. when\r\n            // it is a Refresh Boundary.\r\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\r\n                // Save the previous exports signature on update so we can compare the boundary\r\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\r\n                module.hot.dispose(function (data) {\r\n                    data.prevSignature =\r\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\r\n                });\r\n                // Unconditionally accept an update to this module, we'll check if it's\r\n                // still a Refresh Boundary later.\r\n                // @ts-ignore importMeta is replaced in the loader\r\n                module.hot.accept();\r\n                // This field is set when the previous version of this module was a\r\n                // Refresh Boundary, letting us know we need to check for invalidation or\r\n                // enqueue an update.\r\n                if (prevSignature !== null) {\r\n                    // A boundary can become ineligible if its exports are incompatible\r\n                    // with the previous exports.\r\n                    //\r\n                    // For example, if you add/remove/change exports, we'll want to\r\n                    // re-execute the importing modules, and force those components to\r\n                    // re-render. Similarly, if you convert a class component to a\r\n                    // function, we want to invalidate the boundary.\r\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\r\n                        module.hot.invalidate();\r\n                    }\r\n                    else {\r\n                        self.$RefreshHelpers$.scheduleUpdate();\r\n                    }\r\n                }\r\n            }\r\n            else {\r\n                // Since we just executed the code for the module, it's possible that the\r\n                // new exports made it ineligible for being a boundary.\r\n                // We only care about the case when we were _previously_ a boundary,\r\n                // because we already accepted this update (accidental side effect).\r\n                var isNoLongerABoundary = prevSignature !== null;\r\n                if (isNoLongerABoundary) {\r\n                    module.hot.invalidate();\r\n                }\r\n            }\r\n        }\r\n    })();\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/common/CaseManagementWidget/CaseManagementWidget.tsx\n"));

/***/ })

});