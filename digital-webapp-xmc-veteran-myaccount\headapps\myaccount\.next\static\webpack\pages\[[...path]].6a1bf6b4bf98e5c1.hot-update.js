"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[[...path]]",{

/***/ "./src/components/common/CaseManagementWidget/CaseManagementWidget.tsx":
/*!*****************************************************************************!*\
  !*** ./src/components/common/CaseManagementWidget/CaseManagementWidget.tsx ***!
  \*****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CaseManagementWidget: function() { return /* binding */ CaseManagementWidget; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mantine/core */ \"./node_modules/@mantine/core/esm/index.js\");\n/* harmony import */ var _sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @sitecore-jss/sitecore-jss-nextjs */ \"./node_modules/@sitecore-jss/sitecore-jss-nextjs/dist/esm/index.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/script */ \"./node_modules/next/script.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_script__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var src_hoc_ApplicationInsightsLogger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/hoc/ApplicationInsightsLogger */ \"./src/hoc/ApplicationInsightsLogger.tsx\");\n/* harmony import */ var src_stores_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/stores/store */ \"./src/stores/store.ts\");\n/* harmony import */ var _PageBuilder_PageBuilder__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../PageBuilder/PageBuilder */ \"./src/components/common/PageBuilder/PageBuilder.tsx\");\n\r\nvar _s = $RefreshSig$();\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nconst CaseManagementWidget = (props)=>{\r\n    var _props_fields_LightningScriptUrl, _props_fields;\r\n    _s();\r\n    const context = (0,_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_6__.useSitecoreContext)();\r\n    const isPageEditing = context.sitecoreContext.pageEditing;\r\n    if (isPageEditing) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PageBuilder_PageBuilder__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\r\n        componentName: \"CaseManagementWidget\"\r\n    }, void 0, false, {\r\n        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n        lineNumber: 20,\r\n        columnNumber: 29\r\n    }, undefined);\r\n    let partnerNumber = undefined;\r\n    if (!isPageEditing) {\r\n        partnerNumber = (0,src_stores_store__WEBPACK_IMPORTED_MODULE_4__.useAppSelector)((state)=>{\r\n            var _state_authuser;\r\n            return state === null || state === void 0 ? void 0 : (_state_authuser = state.authuser) === null || _state_authuser === void 0 ? void 0 : _state_authuser.bpNumber;\r\n        });\r\n    }\r\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\r\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\r\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\r\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\r\n    const retryCountRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(0);\r\n    const maxRetries = 3;\r\n    const timeoutDuration = 30000; // 30 seconds\r\n    // Clear timeout on component unmount\r\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\r\n        return ()=>{\r\n            if (timeoutRef.current) {\r\n                clearTimeout(timeoutRef.current);\r\n            }\r\n        };\r\n    }, []);\r\n    function callLightning() {\r\n        try {\r\n            var _props_fields_SalesforceScriptUrl, _props_fields;\r\n            // Set a timeout for the Lightning component loading\r\n            timeoutRef.current = setTimeout(()=>{\r\n                if (isLoading) {\r\n                    console.error(\"Lightning component loading timed out\");\r\n                    setHasError(true);\r\n                    setErrorMessage(\"The customer request center is taking longer than expected to load. Please try refreshing the page.\");\r\n                    setIsLoading(false);\r\n                }\r\n            }, timeoutDuration);\r\n            // Check if Lightning is available\r\n            if (!window.$Lightning) {\r\n                throw new Error(\"Salesforce Lightning framework is not available\");\r\n            }\r\n            window.$Lightning.use(\"c:CXT_SelftServeLtngOut\", ()=>{\r\n                window.$Lightning.createComponent(\"c:cXT_CaseMgmtSelfServe\", {\r\n                    bpNumber: partnerNumber ? partnerNumber : \"\"\r\n                }, \"lightningContainer\", (component)=>{\r\n                    // Clear timeout on successful load\r\n                    if (timeoutRef.current) {\r\n                        clearTimeout(timeoutRef.current);\r\n                    }\r\n                    setIsLoading(false);\r\n                    setHasError(false);\r\n                    console.log(\"Component loaded successfully: \", component);\r\n                });\r\n            }, props === null || props === void 0 ? void 0 : (_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_SalesforceScriptUrl = _props_fields.SalesforceScriptUrl) === null || _props_fields_SalesforceScriptUrl === void 0 ? void 0 : _props_fields_SalesforceScriptUrl.value);\r\n        } catch (error) {\r\n            console.error(\"Error initializing Lightning component:\", error);\r\n            handleError(error);\r\n        }\r\n    }\r\n    function handleError(error) {\r\n        if (timeoutRef.current) {\r\n            clearTimeout(timeoutRef.current);\r\n        }\r\n        retryCountRef.current += 1;\r\n        if (retryCountRef.current < maxRetries) {\r\n            console.log(\"Retrying Lightning component initialization (attempt \".concat(retryCountRef.current + 1, \"/\").concat(maxRetries, \")\"));\r\n            setTimeout(()=>{\r\n                callLightning();\r\n            }, 2000); // Wait 2 seconds before retry\r\n        } else {\r\n            setHasError(true);\r\n            setErrorMessage(\"Failed to load the customer request center after \".concat(maxRetries, \" attempts. Please refresh the page or contact support.\"));\r\n            setIsLoading(false);\r\n        }\r\n    }\r\n    function handleScriptError() {\r\n        console.error(\"Failed to load Salesforce Lightning script\");\r\n        setHasError(true);\r\n        setErrorMessage(\"Failed to load required scripts. Please check your internet connection and try again.\");\r\n        setIsLoading(false);\r\n    }\r\n    function retryLoading() {\r\n        setIsLoading(true);\r\n        setHasError(false);\r\n        setErrorMessage(\"\");\r\n        retryCountRef.current = 0;\r\n        callLightning();\r\n    }\r\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n        className: \"mb-20 sm:mb-0\",\r\n        children: [\r\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                className: \"w-full flex flex-col items-center\",\r\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Loader, {}, void 0, false, {\r\n                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                    lineNumber: 125,\r\n                    columnNumber: 11\r\n                }, undefined)\r\n            }, void 0, false, {\r\n                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                lineNumber: 124,\r\n                columnNumber: 9\r\n            }, undefined),\r\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_script__WEBPACK_IMPORTED_MODULE_1___default()), {\r\n                src: props === null || props === void 0 ? void 0 : (_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_LightningScriptUrl = _props_fields.LightningScriptUrl) === null || _props_fields_LightningScriptUrl === void 0 ? void 0 : _props_fields_LightningScriptUrl.value,\r\n                strategy: \"lazyOnload\",\r\n                onLoad: callLightning\r\n            }, void 0, false, {\r\n                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                lineNumber: 128,\r\n                columnNumber: 7\r\n            }, undefined),\r\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                className: \"w-full sm:w-[905px] case-manage\",\r\n                id: \"lightningContainer\"\r\n            }, void 0, false, {\r\n                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                lineNumber: 133,\r\n                columnNumber: 7\r\n            }, undefined)\r\n        ]\r\n    }, void 0, true, {\r\n        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n        lineNumber: 122,\r\n        columnNumber: 5\r\n    }, undefined);\r\n};\r\n_s(CaseManagementWidget, \"uCBgy6ZhWzIZbuVPSGIKFgISpl4=\", false, function() {\r\n    return [\r\n        _sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_6__.useSitecoreContext\r\n    ];\r\n});\r\n_c = CaseManagementWidget;\r\n\r\nconst Component = (0,_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_6__.withDatasourceCheck)()(CaseManagementWidget);\r\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c1 = (0,src_hoc_ApplicationInsightsLogger__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(Component, Component.name));\r\nvar _c, _c1;\r\n$RefreshReg$(_c, \"CaseManagementWidget\");\r\n$RefreshReg$(_c1, \"%default%\");\r\n\r\n\r\n;\r\n    // Wrapped in an IIFE to avoid polluting the global scope\r\n    ;\r\n    (function () {\r\n        var _a, _b;\r\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\r\n        // to extract CSS. For backwards compatibility, we need to check we're in a\r\n        // browser context before continuing.\r\n        if (typeof self !== 'undefined' &&\r\n            // AMP / No-JS mode does not inject these helpers:\r\n            '$RefreshHelpers$' in self) {\r\n            // @ts-ignore __webpack_module__ is global\r\n            var currentExports = module.exports;\r\n            // @ts-ignore __webpack_module__ is global\r\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\r\n            // This cannot happen in MainTemplate because the exports mismatch between\r\n            // templating and execution.\r\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\r\n            // A module can be accepted automatically based on its exports, e.g. when\r\n            // it is a Refresh Boundary.\r\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\r\n                // Save the previous exports signature on update so we can compare the boundary\r\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\r\n                module.hot.dispose(function (data) {\r\n                    data.prevSignature =\r\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\r\n                });\r\n                // Unconditionally accept an update to this module, we'll check if it's\r\n                // still a Refresh Boundary later.\r\n                // @ts-ignore importMeta is replaced in the loader\r\n                module.hot.accept();\r\n                // This field is set when the previous version of this module was a\r\n                // Refresh Boundary, letting us know we need to check for invalidation or\r\n                // enqueue an update.\r\n                if (prevSignature !== null) {\r\n                    // A boundary can become ineligible if its exports are incompatible\r\n                    // with the previous exports.\r\n                    //\r\n                    // For example, if you add/remove/change exports, we'll want to\r\n                    // re-execute the importing modules, and force those components to\r\n                    // re-render. Similarly, if you convert a class component to a\r\n                    // function, we want to invalidate the boundary.\r\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\r\n                        module.hot.invalidate();\r\n                    }\r\n                    else {\r\n                        self.$RefreshHelpers$.scheduleUpdate();\r\n                    }\r\n                }\r\n            }\r\n            else {\r\n                // Since we just executed the code for the module, it's possible that the\r\n                // new exports made it ineligible for being a boundary.\r\n                // We only care about the case when we were _previously_ a boundary,\r\n                // because we already accepted this update (accidental side effect).\r\n                var isNoLongerABoundary = prevSignature !== null;\r\n                if (isNoLongerABoundary) {\r\n                    module.hot.invalidate();\r\n                }\r\n            }\r\n        }\r\n    })();\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/common/CaseManagementWidget/CaseManagementWidget.tsx\n"));

/***/ })

});