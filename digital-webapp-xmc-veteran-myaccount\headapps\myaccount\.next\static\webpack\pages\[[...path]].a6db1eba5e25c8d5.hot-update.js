"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[[...path]]",{

/***/ "./src/components/common/CaseManagementWidget/CaseManagementWidget.tsx":
/*!*****************************************************************************!*\
  !*** ./src/components/common/CaseManagementWidget/CaseManagementWidget.tsx ***!
  \*****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CaseManagementWidget: function() { return /* binding */ CaseManagementWidget; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mantine/core */ \"./node_modules/@mantine/core/esm/index.js\");\n/* harmony import */ var _sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @sitecore-jss/sitecore-jss-nextjs */ \"./node_modules/@sitecore-jss/sitecore-jss-nextjs/dist/esm/index.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/script */ \"./node_modules/next/script.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_script__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var src_hoc_ApplicationInsightsLogger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/hoc/ApplicationInsightsLogger */ \"./src/hoc/ApplicationInsightsLogger.tsx\");\n/* harmony import */ var src_stores_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/stores/store */ \"./src/stores/store.ts\");\n/* harmony import */ var _PageBuilder_PageBuilder__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../PageBuilder/PageBuilder */ \"./src/components/common/PageBuilder/PageBuilder.tsx\");\n\r\nvar _s = $RefreshSig$();\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nconst CaseManagementWidget = (props)=>{\r\n    var _props_fields_LightningScriptUrl, _props_fields;\r\n    _s();\r\n    const context = (0,_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_6__.useSitecoreContext)();\r\n    const isPageEditing = context.sitecoreContext.pageEditing;\r\n    if (isPageEditing) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PageBuilder_PageBuilder__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\r\n        componentName: \"CaseManagementWidget\"\r\n    }, void 0, false, {\r\n        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n        lineNumber: 20,\r\n        columnNumber: 29\r\n    }, undefined);\r\n    let partnerNumber = undefined;\r\n    if (!isPageEditing) {\r\n        partnerNumber = (0,src_stores_store__WEBPACK_IMPORTED_MODULE_4__.useAppSelector)((state)=>{\r\n            var _state_authuser;\r\n            return state === null || state === void 0 ? void 0 : (_state_authuser = state.authuser) === null || _state_authuser === void 0 ? void 0 : _state_authuser.bpNumber;\r\n        });\r\n    }\r\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\r\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\r\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\r\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\r\n    const retryCountRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(0);\r\n    const maxRetries = 3;\r\n    const timeoutDuration = 30000; // 30 seconds\r\n    // Clear timeout on component unmount\r\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\r\n        return ()=>{\r\n            if (timeoutRef.current) {\r\n                clearTimeout(timeoutRef.current);\r\n            }\r\n        };\r\n    }, []);\r\n    function callLightning() {\r\n        try {\r\n            var _props_fields_SalesforceScriptUrl, _props_fields;\r\n            // Set a timeout for the Lightning component loading\r\n            timeoutRef.current = setTimeout(()=>{\r\n                if (isLoading) {\r\n                    console.error(\"Lightning component loading timed out\");\r\n                    setHasError(true);\r\n                    setErrorMessage(\"The customer request center is taking longer than expected to load. Please try refreshing the page.\");\r\n                    setIsLoading(false);\r\n                }\r\n            }, timeoutDuration);\r\n            // Check if Lightning is available\r\n            if (!window.$Lightning) {\r\n                throw new Error(\"Salesforce Lightning framework is not available\");\r\n            }\r\n            window.$Lightning.use(\"c:CXT_SelftServeLtngOut\", ()=>{\r\n                window.$Lightning.createComponent(\"c:cXT_CaseMgmtSelfServe\", {\r\n                    bpNumber: partnerNumber ? partnerNumber : \"\"\r\n                }, \"lightningContainer\", (component)=>{\r\n                    // Clear timeout on successful load\r\n                    if (timeoutRef.current) {\r\n                        clearTimeout(timeoutRef.current);\r\n                    }\r\n                    setIsLoading(false);\r\n                    setHasError(false);\r\n                    console.log(\"Component loaded successfully: \", component);\r\n                });\r\n            }, props === null || props === void 0 ? void 0 : (_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_SalesforceScriptUrl = _props_fields.SalesforceScriptUrl) === null || _props_fields_SalesforceScriptUrl === void 0 ? void 0 : _props_fields_SalesforceScriptUrl.value);\r\n        } catch (error) {\r\n            console.error(\"Error initializing Lightning component:\", error);\r\n            handleError(error);\r\n        }\r\n    }\r\n    function handleError(error) {\r\n        if (timeoutRef.current) {\r\n            clearTimeout(timeoutRef.current);\r\n        }\r\n        retryCountRef.current += 1;\r\n        if (retryCountRef.current < maxRetries) {\r\n            console.log(\"Retrying Lightning component initialization (attempt \".concat(retryCountRef.current + 1, \"/\").concat(maxRetries, \")\"));\r\n            setTimeout(()=>{\r\n                callLightning();\r\n            }, 2000); // Wait 2 seconds before retry\r\n        } else {\r\n            setHasError(true);\r\n            setErrorMessage(\"Failed to load the customer request center after \".concat(maxRetries, \" attempts. Please refresh the page or contact support.\"));\r\n            setIsLoading(false);\r\n        }\r\n    }\r\n    function handleScriptError() {\r\n        console.error(\"Failed to load Salesforce Lightning script\");\r\n        setHasError(true);\r\n        setErrorMessage(\"Failed to load required scripts. Please check your internet connection and try again.\");\r\n        setIsLoading(false);\r\n    }\r\n    function retryLoading() {\r\n        setIsLoading(true);\r\n        setHasError(false);\r\n        setErrorMessage(\"\");\r\n        retryCountRef.current = 0;\r\n        callLightning();\r\n    }\r\n    // Show error state with retry option\r\n    if (hasError) {\r\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n            className: \"mb-20 sm:mb-0\",\r\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                className: \"w-full sm:w-[905px] case-manage\",\r\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                    className: \"flex flex-col items-center justify-center p-8 bg-red-50 border border-red-200 rounded-lg\",\r\n                    children: [\r\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                            className: \"text-red-600 text-lg font-semibold mb-4\",\r\n                            children: \"Unable to Load Customer Request Center\"\r\n                        }, void 0, false, {\r\n                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                            lineNumber: 127,\r\n                            columnNumber: 13\r\n                        }, undefined),\r\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                            className: \"text-red-700 text-center mb-6 max-w-md\",\r\n                            children: errorMessage\r\n                        }, void 0, false, {\r\n                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                            lineNumber: 130,\r\n                            columnNumber: 13\r\n                        }, undefined),\r\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\r\n                            onClick: retryLoading,\r\n                            className: \"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\r\n                            children: \"Try Again\"\r\n                        }, void 0, false, {\r\n                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                            lineNumber: 133,\r\n                            columnNumber: 13\r\n                        }, undefined),\r\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                            className: \"mt-4 text-sm text-gray-600 text-center\",\r\n                            children: [\r\n                                \"If the problem persists, please contact customer support at\",\r\n                                \" \",\r\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\r\n                                    href: \"tel:**************\",\r\n                                    className: \"text-blue-600 hover:underline\",\r\n                                    children: \"(*************\"\r\n                                }, void 0, false, {\r\n                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                                    lineNumber: 141,\r\n                                    columnNumber: 15\r\n                                }, undefined)\r\n                            ]\r\n                        }, void 0, true, {\r\n                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                            lineNumber: 139,\r\n                            columnNumber: 13\r\n                        }, undefined)\r\n                    ]\r\n                }, void 0, true, {\r\n                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                    lineNumber: 126,\r\n                    columnNumber: 11\r\n                }, undefined)\r\n            }, void 0, false, {\r\n                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                lineNumber: 125,\r\n                columnNumber: 9\r\n            }, undefined)\r\n        }, void 0, false, {\r\n            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n            lineNumber: 124,\r\n            columnNumber: 7\r\n        }, undefined);\r\n    }\r\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n        className: \"mb-20 sm:mb-0\",\r\n        children: [\r\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                className: \"w-full flex flex-col items-center justify-center p-8\",\r\n                children: [\r\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Loader, {}, void 0, false, {\r\n                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                        lineNumber: 155,\r\n                        columnNumber: 11\r\n                    }, undefined),\r\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                        className: \"mt-4 text-gray-600 text-center\",\r\n                        children: \"Loading Customer Request Center...\"\r\n                    }, void 0, false, {\r\n                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                        lineNumber: 156,\r\n                        columnNumber: 11\r\n                    }, undefined),\r\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                        className: \"mt-2 text-sm text-gray-500 text-center\",\r\n                        children: \"This may take a few moments\"\r\n                    }, void 0, false, {\r\n                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                        lineNumber: 159,\r\n                        columnNumber: 11\r\n                    }, undefined)\r\n                ]\r\n            }, void 0, true, {\r\n                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                lineNumber: 154,\r\n                columnNumber: 9\r\n            }, undefined),\r\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_script__WEBPACK_IMPORTED_MODULE_1___default()), {\r\n                src: props === null || props === void 0 ? void 0 : (_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_LightningScriptUrl = _props_fields.LightningScriptUrl) === null || _props_fields_LightningScriptUrl === void 0 ? void 0 : _props_fields_LightningScriptUrl.value,\r\n                strategy: \"lazyOnload\",\r\n                onLoad: callLightning,\r\n                onError: handleScriptError\r\n            }, void 0, false, {\r\n                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                lineNumber: 164,\r\n                columnNumber: 7\r\n            }, undefined),\r\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                className: \"w-full sm:w-[905px] case-manage\",\r\n                id: \"lightningContainer\"\r\n            }, void 0, false, {\r\n                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                lineNumber: 170,\r\n                columnNumber: 7\r\n            }, undefined)\r\n        ]\r\n    }, void 0, true, {\r\n        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n        lineNumber: 152,\r\n        columnNumber: 5\r\n    }, undefined);\r\n};\r\n_s(CaseManagementWidget, \"uCBgy6ZhWzIZbuVPSGIKFgISpl4=\", false, function() {\r\n    return [\r\n        _sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_6__.useSitecoreContext\r\n    ];\r\n});\r\n_c = CaseManagementWidget;\r\n\r\nconst Component = (0,_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_6__.withDatasourceCheck)()(CaseManagementWidget);\r\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c1 = (0,src_hoc_ApplicationInsightsLogger__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(Component, Component.name));\r\nvar _c, _c1;\r\n$RefreshReg$(_c, \"CaseManagementWidget\");\r\n$RefreshReg$(_c1, \"%default%\");\r\n\r\n\r\n;\r\n    // Wrapped in an IIFE to avoid polluting the global scope\r\n    ;\r\n    (function () {\r\n        var _a, _b;\r\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\r\n        // to extract CSS. For backwards compatibility, we need to check we're in a\r\n        // browser context before continuing.\r\n        if (typeof self !== 'undefined' &&\r\n            // AMP / No-JS mode does not inject these helpers:\r\n            '$RefreshHelpers$' in self) {\r\n            // @ts-ignore __webpack_module__ is global\r\n            var currentExports = module.exports;\r\n            // @ts-ignore __webpack_module__ is global\r\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\r\n            // This cannot happen in MainTemplate because the exports mismatch between\r\n            // templating and execution.\r\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\r\n            // A module can be accepted automatically based on its exports, e.g. when\r\n            // it is a Refresh Boundary.\r\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\r\n                // Save the previous exports signature on update so we can compare the boundary\r\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\r\n                module.hot.dispose(function (data) {\r\n                    data.prevSignature =\r\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\r\n                });\r\n                // Unconditionally accept an update to this module, we'll check if it's\r\n                // still a Refresh Boundary later.\r\n                // @ts-ignore importMeta is replaced in the loader\r\n                module.hot.accept();\r\n                // This field is set when the previous version of this module was a\r\n                // Refresh Boundary, letting us know we need to check for invalidation or\r\n                // enqueue an update.\r\n                if (prevSignature !== null) {\r\n                    // A boundary can become ineligible if its exports are incompatible\r\n                    // with the previous exports.\r\n                    //\r\n                    // For example, if you add/remove/change exports, we'll want to\r\n                    // re-execute the importing modules, and force those components to\r\n                    // re-render. Similarly, if you convert a class component to a\r\n                    // function, we want to invalidate the boundary.\r\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\r\n                        module.hot.invalidate();\r\n                    }\r\n                    else {\r\n                        self.$RefreshHelpers$.scheduleUpdate();\r\n                    }\r\n                }\r\n            }\r\n            else {\r\n                // Since we just executed the code for the module, it's possible that the\r\n                // new exports made it ineligible for being a boundary.\r\n                // We only care about the case when we were _previously_ a boundary,\r\n                // because we already accepted this update (accidental side effect).\r\n                var isNoLongerABoundary = prevSignature !== null;\r\n                if (isNoLongerABoundary) {\r\n                    module.hot.invalidate();\r\n                }\r\n            }\r\n        }\r\n    })();\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/common/CaseManagementWidget/CaseManagementWidget.tsx\n"));

/***/ })

});