"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[[...path]]",{

/***/ "./src/components/common/CaseManagementWidget/CaseManagementWidget.tsx":
/*!*****************************************************************************!*\
  !*** ./src/components/common/CaseManagementWidget/CaseManagementWidget.tsx ***!
  \*****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CaseManagementWidget: function() { return /* binding */ CaseManagementWidget; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mantine/core */ \"./node_modules/@mantine/core/esm/index.js\");\n/* harmony import */ var _sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @sitecore-jss/sitecore-jss-nextjs */ \"./node_modules/@sitecore-jss/sitecore-jss-nextjs/dist/esm/index.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/script */ \"./node_modules/next/script.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_script__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var src_hoc_ApplicationInsightsLogger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/hoc/ApplicationInsightsLogger */ \"./src/hoc/ApplicationInsightsLogger.tsx\");\n/* harmony import */ var src_stores_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/stores/store */ \"./src/stores/store.ts\");\n/* harmony import */ var _PageBuilder_PageBuilder__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../PageBuilder/PageBuilder */ \"./src/components/common/PageBuilder/PageBuilder.tsx\");\n\r\nvar _s = $RefreshSig$();\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nconst CaseManagementWidget = (props)=>{\r\n    var _props_fields_LightningScriptUrl, _props_fields;\r\n    _s();\r\n    const context = (0,_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_6__.useSitecoreContext)();\r\n    const isPageEditing = context.sitecoreContext.pageEditing;\r\n    if (isPageEditing) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PageBuilder_PageBuilder__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\r\n        componentName: \"CaseManagementWidget\"\r\n    }, void 0, false, {\r\n        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n        lineNumber: 20,\r\n        columnNumber: 29\r\n    }, undefined);\r\n    let partnerNumber = undefined;\r\n    if (!isPageEditing) {\r\n        partnerNumber = (0,src_stores_store__WEBPACK_IMPORTED_MODULE_4__.useAppSelector)((state)=>{\r\n            var _state_authuser;\r\n            return state === null || state === void 0 ? void 0 : (_state_authuser = state.authuser) === null || _state_authuser === void 0 ? void 0 : _state_authuser.bpNumber;\r\n        });\r\n    }\r\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\r\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\r\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\r\n    const timeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\r\n    const retryCountRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(0);\r\n    const maxRetries = 3;\r\n    const timeoutDuration = 30000; // 30 seconds\r\n    // Clear timeout on component unmount\r\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\r\n        return ()=>{\r\n            if (timeoutRef.current) {\r\n                clearTimeout(timeoutRef.current);\r\n            }\r\n        };\r\n    }, []);\r\n    function callLightning() {\r\n        try {\r\n            var _props_fields_SalesforceScriptUrl, _props_fields, _props_fields_LightningScriptUrl, _props_fields1, _props_fields_LightningScriptUrl1, _props_fields2, _props_fields_SalesforceScriptUrl1, _props_fields3, _props_fields_SalesforceScriptUrl2, _props_fields4;\r\n            console.log(\"Initializing Lightning component...\", {\r\n                partnerNumber,\r\n                salesforceScriptUrl: props === null || props === void 0 ? void 0 : (_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_SalesforceScriptUrl = _props_fields.SalesforceScriptUrl) === null || _props_fields_SalesforceScriptUrl === void 0 ? void 0 : _props_fields_SalesforceScriptUrl.value,\r\n                lightningScriptUrl: props === null || props === void 0 ? void 0 : (_props_fields1 = props.fields) === null || _props_fields1 === void 0 ? void 0 : (_props_fields_LightningScriptUrl = _props_fields1.LightningScriptUrl) === null || _props_fields_LightningScriptUrl === void 0 ? void 0 : _props_fields_LightningScriptUrl.value,\r\n                windowLightning: !!window.$Lightning,\r\n                containerExists: !!document.getElementById(\"lightningContainer\")\r\n            });\r\n            // Validate required URLs\r\n            if (!(props === null || props === void 0 ? void 0 : (_props_fields2 = props.fields) === null || _props_fields2 === void 0 ? void 0 : (_props_fields_LightningScriptUrl1 = _props_fields2.LightningScriptUrl) === null || _props_fields_LightningScriptUrl1 === void 0 ? void 0 : _props_fields_LightningScriptUrl1.value)) {\r\n                throw new Error(\"Lightning Script URL is not configured\");\r\n            }\r\n            if (!(props === null || props === void 0 ? void 0 : (_props_fields3 = props.fields) === null || _props_fields3 === void 0 ? void 0 : (_props_fields_SalesforceScriptUrl1 = _props_fields3.SalesforceScriptUrl) === null || _props_fields_SalesforceScriptUrl1 === void 0 ? void 0 : _props_fields_SalesforceScriptUrl1.value)) {\r\n                throw new Error(\"Salesforce Script URL is not configured\");\r\n            }\r\n            // Set a timeout for the Lightning component loading\r\n            timeoutRef.current = setTimeout(()=>{\r\n                if (isLoading) {\r\n                    console.error(\"Lightning component loading timed out after\", timeoutDuration, \"ms\");\r\n                    console.log(\"Debug info at timeout:\", {\r\n                        windowLightning: !!window.$Lightning,\r\n                        containerExists: !!document.getElementById(\"lightningContainer\"),\r\n                        partnerNumber,\r\n                        retryCount: retryCountRef.current\r\n                    });\r\n                    setHasError(true);\r\n                    setErrorMessage(\"The customer request center is taking longer than expected to load. Please try refreshing the page.\");\r\n                    setIsLoading(false);\r\n                }\r\n            }, timeoutDuration);\r\n            // Check if Lightning is available\r\n            if (!window.$Lightning) {\r\n                throw new Error(\"Salesforce Lightning framework is not available. Please ensure the Lightning script has loaded.\");\r\n            }\r\n            console.log(\"Lightning framework available, creating component...\");\r\n            window.$Lightning.use(\"c:CXT_SelftServeLtngOut\", ()=>{\r\n                console.log(\"Lightning app loaded, creating component...\");\r\n                // Ensure container still exists\r\n                const container = document.getElementById(\"lightningContainer\");\r\n                if (!container) {\r\n                    throw new Error(\"Lightning container element not found\");\r\n                }\r\n                window.$Lightning.createComponent(\"c:cXT_CaseMgmtSelfServe\", {\r\n                    bpNumber: partnerNumber ? partnerNumber : \"\"\r\n                }, \"lightningContainer\", (component)=>{\r\n                    // Clear timeout on successful load\r\n                    if (timeoutRef.current) {\r\n                        clearTimeout(timeoutRef.current);\r\n                    }\r\n                    setIsLoading(false);\r\n                    setHasError(false);\r\n                    console.log(\"Component loaded successfully: \", component);\r\n                });\r\n            }, props === null || props === void 0 ? void 0 : (_props_fields4 = props.fields) === null || _props_fields4 === void 0 ? void 0 : (_props_fields_SalesforceScriptUrl2 = _props_fields4.SalesforceScriptUrl) === null || _props_fields_SalesforceScriptUrl2 === void 0 ? void 0 : _props_fields_SalesforceScriptUrl2.value);\r\n        } catch (error) {\r\n            console.error(\"Error initializing Lightning component:\", error);\r\n            handleError(error);\r\n        }\r\n    }\r\n    function handleError(error) {\r\n        console.error(\"Lightning component error:\", error.message);\r\n        if (timeoutRef.current) {\r\n            clearTimeout(timeoutRef.current);\r\n        }\r\n        retryCountRef.current += 1;\r\n        if (retryCountRef.current < maxRetries) {\r\n            console.log(\"Retrying Lightning component initialization (attempt \".concat(retryCountRef.current + 1, \"/\").concat(maxRetries, \") after error: \").concat(error.message));\r\n            setTimeout(()=>{\r\n                callLightning();\r\n            }, 2000); // Wait 2 seconds before retry\r\n        } else {\r\n            setHasError(true);\r\n            setErrorMessage(\"Failed to load the customer request center after \".concat(maxRetries, \" attempts. Error: \").concat(error.message, \". Please refresh the page or contact support.\"));\r\n            setIsLoading(false);\r\n        }\r\n    }\r\n    function handleScriptError() {\r\n        console.error(\"Failed to load Salesforce Lightning script\");\r\n        setHasError(true);\r\n        setErrorMessage(\"Failed to load required scripts. Please check your internet connection and try again.\");\r\n        setIsLoading(false);\r\n    }\r\n    function retryLoading() {\r\n        setIsLoading(true);\r\n        setHasError(false);\r\n        setErrorMessage(\"\");\r\n        retryCountRef.current = 0;\r\n        callLightning();\r\n    }\r\n    // Show error state with retry option and alternative contact methods\r\n    if (hasError) {\r\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n            className: \"mb-20 sm:mb-0\",\r\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                className: \"w-full sm:w-[905px] case-manage\",\r\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                    className: \"flex flex-col items-center justify-center p-8 bg-red-50 border border-red-200 rounded-lg\",\r\n                    children: [\r\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                            className: \"text-red-600 text-lg font-semibold mb-4\",\r\n                            children: \"Unable to Load Customer Request Center\"\r\n                        }, void 0, false, {\r\n                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                            lineNumber: 161,\r\n                            columnNumber: 13\r\n                        }, undefined),\r\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                            className: \"text-red-700 text-center mb-6 max-w-md\",\r\n                            children: errorMessage\r\n                        }, void 0, false, {\r\n                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                            lineNumber: 164,\r\n                            columnNumber: 13\r\n                        }, undefined),\r\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\r\n                            onClick: retryLoading,\r\n                            className: \"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mb-6\",\r\n                            children: \"Try Again\"\r\n                        }, void 0, false, {\r\n                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                            lineNumber: 167,\r\n                            columnNumber: 13\r\n                        }, undefined),\r\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                            className: \"w-full max-w-md bg-white border border-gray-200 rounded-lg p-6\",\r\n                            children: [\r\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\r\n                                    className: \"text-lg font-semibold text-gray-800 mb-4 text-center\",\r\n                                    children: \"Alternative Ways to Get Help\"\r\n                                }, void 0, false, {\r\n                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                                    lineNumber: 176,\r\n                                    columnNumber: 15\r\n                                }, undefined),\r\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                    className: \"space-y-4\",\r\n                                    children: [\r\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                            className: \"flex items-center justify-between p-3 bg-blue-50 rounded-lg\",\r\n                                            children: [\r\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\r\n                                                    className: \"text-gray-700\",\r\n                                                    children: \"Call Customer Service\"\r\n                                                }, void 0, false, {\r\n                                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                                                    lineNumber: 181,\r\n                                                    columnNumber: 19\r\n                                                }, undefined),\r\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\r\n                                                    href: \"tel:**************\",\r\n                                                    className: \"text-blue-600 hover:underline font-semibold\",\r\n                                                    children: \"(*************\"\r\n                                                }, void 0, false, {\r\n                                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                                                    lineNumber: 182,\r\n                                                    columnNumber: 19\r\n                                                }, undefined)\r\n                                            ]\r\n                                        }, void 0, true, {\r\n                                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                                            lineNumber: 180,\r\n                                            columnNumber: 17\r\n                                        }, undefined),\r\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                            className: \"flex items-center justify-between p-3 bg-green-50 rounded-lg\",\r\n                                            children: [\r\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\r\n                                                    className: \"text-gray-700\",\r\n                                                    children: \"Email Support\"\r\n                                                }, void 0, false, {\r\n                                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                                                    lineNumber: 190,\r\n                                                    columnNumber: 19\r\n                                                }, undefined),\r\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\r\n                                                    href: \"mailto:<EMAIL>\",\r\n                                                    className: \"text-green-600 hover:underline font-semibold\",\r\n                                                    children: \"Send Email\"\r\n                                                }, void 0, false, {\r\n                                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                                                    lineNumber: 191,\r\n                                                    columnNumber: 19\r\n                                                }, undefined)\r\n                                            ]\r\n                                        }, void 0, true, {\r\n                                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                                            lineNumber: 189,\r\n                                            columnNumber: 17\r\n                                        }, undefined),\r\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                                            className: \"text-center text-sm text-gray-600 mt-4\",\r\n                                            children: \"Customer service hours: Monday - Friday, 8 AM - 8 PM CT\"\r\n                                        }, void 0, false, {\r\n                                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                                            lineNumber: 198,\r\n                                            columnNumber: 17\r\n                                        }, undefined)\r\n                                    ]\r\n                                }, void 0, true, {\r\n                                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                                    lineNumber: 179,\r\n                                    columnNumber: 15\r\n                                }, undefined)\r\n                            ]\r\n                        }, void 0, true, {\r\n                            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                            lineNumber: 175,\r\n                            columnNumber: 13\r\n                        }, undefined)\r\n                    ]\r\n                }, void 0, true, {\r\n                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                    lineNumber: 160,\r\n                    columnNumber: 11\r\n                }, undefined)\r\n            }, void 0, false, {\r\n                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                lineNumber: 159,\r\n                columnNumber: 9\r\n            }, undefined)\r\n        }, void 0, false, {\r\n            fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n            lineNumber: 158,\r\n            columnNumber: 7\r\n        }, undefined);\r\n    }\r\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n        className: \"mb-20 sm:mb-0\",\r\n        children: [\r\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                className: \"w-full flex flex-col items-center justify-center p-8\",\r\n                children: [\r\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Loader, {}, void 0, false, {\r\n                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                        lineNumber: 213,\r\n                        columnNumber: 11\r\n                    }, undefined),\r\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                        className: \"mt-4 text-gray-600 text-center\",\r\n                        children: \"Loading Customer Request Center...\"\r\n                    }, void 0, false, {\r\n                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                        lineNumber: 214,\r\n                        columnNumber: 11\r\n                    }, undefined),\r\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                        className: \"mt-2 text-sm text-gray-500 text-center\",\r\n                        children: \"This may take a few moments\"\r\n                    }, void 0, false, {\r\n                        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                        lineNumber: 217,\r\n                        columnNumber: 11\r\n                    }, undefined)\r\n                ]\r\n            }, void 0, true, {\r\n                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                lineNumber: 212,\r\n                columnNumber: 9\r\n            }, undefined),\r\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_script__WEBPACK_IMPORTED_MODULE_1___default()), {\r\n                src: props === null || props === void 0 ? void 0 : (_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_LightningScriptUrl = _props_fields.LightningScriptUrl) === null || _props_fields_LightningScriptUrl === void 0 ? void 0 : _props_fields_LightningScriptUrl.value,\r\n                strategy: \"lazyOnload\",\r\n                onLoad: callLightning,\r\n                onError: handleScriptError\r\n            }, void 0, false, {\r\n                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                lineNumber: 222,\r\n                columnNumber: 7\r\n            }, undefined),\r\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                className: \"w-full sm:w-[905px] case-manage\",\r\n                id: \"lightningContainer\"\r\n            }, void 0, false, {\r\n                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                lineNumber: 228,\r\n                columnNumber: 7\r\n            }, undefined)\r\n        ]\r\n    }, void 0, true, {\r\n        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n        lineNumber: 210,\r\n        columnNumber: 5\r\n    }, undefined);\r\n};\r\n_s(CaseManagementWidget, \"uCBgy6ZhWzIZbuVPSGIKFgISpl4=\", false, function() {\r\n    return [\r\n        _sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_6__.useSitecoreContext\r\n    ];\r\n});\r\n_c = CaseManagementWidget;\r\n\r\nconst Component = (0,_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_6__.withDatasourceCheck)()(CaseManagementWidget);\r\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c1 = (0,src_hoc_ApplicationInsightsLogger__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(Component, Component.name));\r\nvar _c, _c1;\r\n$RefreshReg$(_c, \"CaseManagementWidget\");\r\n$RefreshReg$(_c1, \"%default%\");\r\n\r\n\r\n;\r\n    // Wrapped in an IIFE to avoid polluting the global scope\r\n    ;\r\n    (function () {\r\n        var _a, _b;\r\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\r\n        // to extract CSS. For backwards compatibility, we need to check we're in a\r\n        // browser context before continuing.\r\n        if (typeof self !== 'undefined' &&\r\n            // AMP / No-JS mode does not inject these helpers:\r\n            '$RefreshHelpers$' in self) {\r\n            // @ts-ignore __webpack_module__ is global\r\n            var currentExports = module.exports;\r\n            // @ts-ignore __webpack_module__ is global\r\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\r\n            // This cannot happen in MainTemplate because the exports mismatch between\r\n            // templating and execution.\r\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\r\n            // A module can be accepted automatically based on its exports, e.g. when\r\n            // it is a Refresh Boundary.\r\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\r\n                // Save the previous exports signature on update so we can compare the boundary\r\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\r\n                module.hot.dispose(function (data) {\r\n                    data.prevSignature =\r\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\r\n                });\r\n                // Unconditionally accept an update to this module, we'll check if it's\r\n                // still a Refresh Boundary later.\r\n                // @ts-ignore importMeta is replaced in the loader\r\n                module.hot.accept();\r\n                // This field is set when the previous version of this module was a\r\n                // Refresh Boundary, letting us know we need to check for invalidation or\r\n                // enqueue an update.\r\n                if (prevSignature !== null) {\r\n                    // A boundary can become ineligible if its exports are incompatible\r\n                    // with the previous exports.\r\n                    //\r\n                    // For example, if you add/remove/change exports, we'll want to\r\n                    // re-execute the importing modules, and force those components to\r\n                    // re-render. Similarly, if you convert a class component to a\r\n                    // function, we want to invalidate the boundary.\r\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\r\n                        module.hot.invalidate();\r\n                    }\r\n                    else {\r\n                        self.$RefreshHelpers$.scheduleUpdate();\r\n                    }\r\n                }\r\n            }\r\n            else {\r\n                // Since we just executed the code for the module, it's possible that the\r\n                // new exports made it ineligible for being a boundary.\r\n                // We only care about the case when we were _previously_ a boundary,\r\n                // because we already accepted this update (accidental side effect).\r\n                var isNoLongerABoundary = prevSignature !== null;\r\n                if (isNoLongerABoundary) {\r\n                    module.hot.invalidate();\r\n                }\r\n            }\r\n        }\r\n    })();\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/common/CaseManagementWidget/CaseManagementWidget.tsx\n"));

/***/ })

});