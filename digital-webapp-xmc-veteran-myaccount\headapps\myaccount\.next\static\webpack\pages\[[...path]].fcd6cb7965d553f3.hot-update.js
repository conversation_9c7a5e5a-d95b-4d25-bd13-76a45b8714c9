"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[[...path]]",{

/***/ "./src/components/common/CaseManagementWidget/CaseManagementWidget.tsx":
/*!*****************************************************************************!*\
  !*** ./src/components/common/CaseManagementWidget/CaseManagementWidget.tsx ***!
  \*****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CaseManagementWidget: function() { return /* binding */ CaseManagementWidget; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _mantine_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @mantine/core */ \"./node_modules/@mantine/core/esm/index.js\");\n/* harmony import */ var _sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @sitecore-jss/sitecore-jss-nextjs */ \"./node_modules/@sitecore-jss/sitecore-jss-nextjs/dist/esm/index.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/script */ \"./node_modules/next/script.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_script__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var src_hoc_ApplicationInsightsLogger__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! src/hoc/ApplicationInsightsLogger */ \"./src/hoc/ApplicationInsightsLogger.tsx\");\n/* harmony import */ var src_stores_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/stores/store */ \"./src/stores/store.ts\");\n/* harmony import */ var _PageBuilder_PageBuilder__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../PageBuilder/PageBuilder */ \"./src/components/common/PageBuilder/PageBuilder.tsx\");\n\r\nvar _s = $RefreshSig$();\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\nconst CaseManagementWidget = (props)=>{\r\n    var _props_fields_LightningScriptUrl, _props_fields;\r\n    _s();\r\n    const context = (0,_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_6__.useSitecoreContext)();\r\n    const isPageEditing = context.sitecoreContext.pageEditing;\r\n    if (isPageEditing) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PageBuilder_PageBuilder__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\r\n        componentName: \"CaseManagementWidget\"\r\n    }, void 0, false, {\r\n        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n        lineNumber: 19,\r\n        columnNumber: 29\r\n    }, undefined);\r\n    let partnerNumber = undefined;\r\n    if (!isPageEditing) {\r\n        partnerNumber = (0,src_stores_store__WEBPACK_IMPORTED_MODULE_4__.useAppSelector)((state)=>{\r\n            var _state_authuser;\r\n            return state === null || state === void 0 ? void 0 : (_state_authuser = state.authuser) === null || _state_authuser === void 0 ? void 0 : _state_authuser.bpNumber;\r\n        });\r\n    }\r\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\r\n    function callLightning() {\r\n        var _props_fields_SalesforceScriptUrl, _props_fields;\r\n        window.$Lightning.use(\"c:CXT_SelftServeLtngOut\", ()=>{\r\n            window.$Lightning.createComponent(\"c:cXT_CaseMgmtSelfServe\", {\r\n                bpNumber: partnerNumber ? partnerNumber : \"\"\r\n            }, \"lightningContainer\", (component)=>{\r\n                setIsLoading(false);\r\n                console.log(\"Component loaded: \", component);\r\n            });\r\n        }, props === null || props === void 0 ? void 0 : (_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_SalesforceScriptUrl = _props_fields.SalesforceScriptUrl) === null || _props_fields_SalesforceScriptUrl === void 0 ? void 0 : _props_fields_SalesforceScriptUrl.value);\r\n    }\r\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n        className: \"mb-20 sm:mb-0\",\r\n        children: [\r\n            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                className: \"w-full flex flex-col items-center\",\r\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mantine_core__WEBPACK_IMPORTED_MODULE_7__.Loader, {}, void 0, false, {\r\n                    fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                    lineNumber: 49,\r\n                    columnNumber: 11\r\n                }, undefined)\r\n            }, void 0, false, {\r\n                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                lineNumber: 48,\r\n                columnNumber: 9\r\n            }, undefined),\r\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_script__WEBPACK_IMPORTED_MODULE_1___default()), {\r\n                src: props === null || props === void 0 ? void 0 : (_props_fields = props.fields) === null || _props_fields === void 0 ? void 0 : (_props_fields_LightningScriptUrl = _props_fields.LightningScriptUrl) === null || _props_fields_LightningScriptUrl === void 0 ? void 0 : _props_fields_LightningScriptUrl.value,\r\n                strategy: \"lazyOnload\",\r\n                onLoad: callLightning\r\n            }, void 0, false, {\r\n                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                lineNumber: 52,\r\n                columnNumber: 7\r\n            }, undefined),\r\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\r\n                className: \"w-full sm:w-[905px] case-manage\",\r\n                id: \"lightningContainer\"\r\n            }, void 0, false, {\r\n                fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n                lineNumber: 57,\r\n                columnNumber: 7\r\n            }, undefined)\r\n        ]\r\n    }, void 0, true, {\r\n        fileName: \"E:\\\\vet_myaccount\\\\digital-webapp-xmc-veteran-myaccount\\\\headapps\\\\myaccount\\\\src\\\\components\\\\common\\\\CaseManagementWidget\\\\CaseManagementWidget.tsx\",\r\n        lineNumber: 46,\r\n        columnNumber: 5\r\n    }, undefined);\r\n};\r\n_s(CaseManagementWidget, \"etlelbJC7gsU5cWKKqZfORQtD4E=\", false, function() {\r\n    return [\r\n        _sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_6__.useSitecoreContext\r\n    ];\r\n});\r\n_c = CaseManagementWidget;\r\n\r\nconst Component = (0,_sitecore_jss_sitecore_jss_nextjs__WEBPACK_IMPORTED_MODULE_6__.withDatasourceCheck)()(CaseManagementWidget);\r\n/* harmony default export */ __webpack_exports__[\"default\"] = (_c1 = (0,src_hoc_ApplicationInsightsLogger__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(Component, Component.name));\r\nvar _c, _c1;\r\n$RefreshReg$(_c, \"CaseManagementWidget\");\r\n$RefreshReg$(_c1, \"%default%\");\r\n\r\n\r\n;\r\n    // Wrapped in an IIFE to avoid polluting the global scope\r\n    ;\r\n    (function () {\r\n        var _a, _b;\r\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\r\n        // to extract CSS. For backwards compatibility, we need to check we're in a\r\n        // browser context before continuing.\r\n        if (typeof self !== 'undefined' &&\r\n            // AMP / No-JS mode does not inject these helpers:\r\n            '$RefreshHelpers$' in self) {\r\n            // @ts-ignore __webpack_module__ is global\r\n            var currentExports = module.exports;\r\n            // @ts-ignore __webpack_module__ is global\r\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\r\n            // This cannot happen in MainTemplate because the exports mismatch between\r\n            // templating and execution.\r\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\r\n            // A module can be accepted automatically based on its exports, e.g. when\r\n            // it is a Refresh Boundary.\r\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\r\n                // Save the previous exports signature on update so we can compare the boundary\r\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\r\n                module.hot.dispose(function (data) {\r\n                    data.prevSignature =\r\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\r\n                });\r\n                // Unconditionally accept an update to this module, we'll check if it's\r\n                // still a Refresh Boundary later.\r\n                // @ts-ignore importMeta is replaced in the loader\r\n                module.hot.accept();\r\n                // This field is set when the previous version of this module was a\r\n                // Refresh Boundary, letting us know we need to check for invalidation or\r\n                // enqueue an update.\r\n                if (prevSignature !== null) {\r\n                    // A boundary can become ineligible if its exports are incompatible\r\n                    // with the previous exports.\r\n                    //\r\n                    // For example, if you add/remove/change exports, we'll want to\r\n                    // re-execute the importing modules, and force those components to\r\n                    // re-render. Similarly, if you convert a class component to a\r\n                    // function, we want to invalidate the boundary.\r\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\r\n                        module.hot.invalidate();\r\n                    }\r\n                    else {\r\n                        self.$RefreshHelpers$.scheduleUpdate();\r\n                    }\r\n                }\r\n            }\r\n            else {\r\n                // Since we just executed the code for the module, it's possible that the\r\n                // new exports made it ineligible for being a boundary.\r\n                // We only care about the case when we were _previously_ a boundary,\r\n                // because we already accepted this update (accidental side effect).\r\n                var isNoLongerABoundary = prevSignature !== null;\r\n                if (isNoLongerABoundary) {\r\n                    module.hot.invalidate();\r\n                }\r\n            }\r\n        }\r\n    })();\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/common/CaseManagementWidget/CaseManagementWidget.tsx\n"));

/***/ })

});